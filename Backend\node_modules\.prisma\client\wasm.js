
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('@prisma/client/runtime/wasm-engine-edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.16.2
 * Query Engine version: 1c57fdcd7e44b29b9313256c76699e91c3ac3c43
 */
Prisma.prismaVersion = {
  client: "6.16.2",
  engine: "1c57fdcd7e44b29b9313256c76699e91c3ac3c43"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  role: 'role',
  deletedAt: 'deletedAt',
  googleId: 'googleId',
  googleAccessToken: 'googleAccessToken',
  googleRefreshToken: 'googleRefreshToken',
  appleId: 'appleId',
  appleRefreshToken: 'appleRefreshToken',
  appleEmail: 'appleEmail',
  appleGivenName: 'appleGivenName',
  appleFamilyName: 'appleFamilyName',
  profilePictureUrl: 'profilePictureUrl',
  provider: 'provider'
};

exports.Prisma.PracticeSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionDate: 'sessionDate',
  totalQuestions: 'totalQuestions',
  correctAnswers: 'correctAnswers',
  totalTimeSpent: 'totalTimeSpent',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PracticeResponseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  questionId: 'questionId',
  practiceSessionId: 'practiceSessionId',
  questionType: 'questionType',
  userResponse: 'userResponse',
  timeTakenSeconds: 'timeTakenSeconds',
  isCorrect: 'isCorrect',
  score: 'score',
  createdAt: 'createdAt'
};

exports.Prisma.OtpCodeScalarFieldEnum = {
  id: 'id',
  email: 'email',
  code: 'code',
  type: 'type',
  expiresAt: 'expiresAt',
  used: 'used',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  planName: 'planName',
  price: 'price',
  durationInDays: 'durationInDays',
  features: 'features',
  userId: 'userId',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripeCustomerId: 'stripeCustomerId',
  isTrial: 'isTrial',
  trialStartedAt: 'trialStartedAt',
  trialEndsAt: 'trialEndsAt',
  trialConvertedToPaid: 'trialConvertedToPaid',
  trialCancellationReason: 'trialCancellationReason'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  coursePreviewVideoUrl: 'coursePreviewVideoUrl',
  isFree: 'isFree',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  imageUrl: 'imageUrl',
  averageRating: 'averageRating',
  reviewCount: 'reviewCount',
  price: 'price',
  currency: 'currency',
  stripeProductId: 'stripeProductId',
  stripePriceId: 'stripePriceId',
  categoryIds: 'categoryIds'
};

exports.Prisma.CourseSectionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  videoUrl: 'videoUrl',
  videoKey: 'videoKey',
  courseId: 'courseId',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  videoUrl: 'videoUrl',
  videoKey: 'videoKey',
  textContent: 'textContent',
  audioUrl: 'audioUrl',
  sectionId: 'sectionId',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCourseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  progress: 'progress',
  completed: 'completed',
  enrolledAt: 'enrolledAt',
  completedAt: 'completedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseReviewScalarFieldEnum = {
  id: 'id',
  rating: 'rating',
  comment: 'comment',
  userId: 'userId',
  courseId: 'courseId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserLessonProgressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  lessonId: 'lessonId',
  isCompleted: 'isCompleted',
  watchedDuration: 'watchedDuration',
  completedAt: 'completedAt',
  lastAccessedAt: 'lastAccessedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSectionProgressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sectionId: 'sectionId',
  isCompleted: 'isCompleted',
  completedAt: 'completedAt',
  lastAccessedAt: 'lastAccessedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonResourceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  lessonId: 'lessonId',
  createdAt: 'createdAt'
};

exports.Prisma.PteSectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  durationMinutes: 'durationMinutes',
  order: 'order'
};

exports.Prisma.QuestionTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  pteSectionId: 'pteSectionId',
  expectedTimePerQuestion: 'expectedTimePerQuestion'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  questionCode: 'questionCode',
  questionTypeId: 'questionTypeId',
  difficultyLevel: 'difficultyLevel',
  textContent: 'textContent',
  questionStatement: 'questionStatement',
  audioUrl: 'audioUrl',
  imageUrl: 'imageUrl',
  options: 'options',
  correctAnswers: 'correctAnswers',
  wordCountMin: 'wordCountMin',
  wordCountMax: 'wordCountMax',
  durationMillis: 'durationMillis',
  originalTextWithErrors: 'originalTextWithErrors',
  incorrectWords: 'incorrectWords',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  testType: 'testType',
  totalDuration: 'totalDuration',
  isFree: 'isFree',
  questionIds: 'questionIds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestAttemptScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  testId: 'testId',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  overallScore: 'overallScore',
  speakingScore: 'speakingScore',
  writingScore: 'writingScore',
  readingScore: 'readingScore',
  listeningScore: 'listeningScore',
  grammarScore: 'grammarScore',
  oralFluencyScore: 'oralFluencyScore',
  pronunciationScore: 'pronunciationScore',
  vocabularyScore: 'vocabularyScore',
  discourseScore: 'discourseScore',
  spellingScore: 'spellingScore',
  rawPteScoreJson: 'rawPteScoreJson',
  status: 'status',
  timeTakenSeconds: 'timeTakenSeconds'
};

exports.Prisma.UserResponseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  questionId: 'questionId',
  textResponse: 'textResponse',
  audioResponseUrl: 'audioResponseUrl',
  selectedOptions: 'selectedOptions',
  orderedItems: 'orderedItems',
  highlightedWords: 'highlightedWords',
  questionScore: 'questionScore',
  isCorrect: 'isCorrect',
  aiFeedback: 'aiFeedback',
  detailedAnalysis: 'detailedAnalysis',
  timeTakenSeconds: 'timeTakenSeconds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuestionResponseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  questionId: 'questionId',
  textResponse: 'textResponse',
  audioResponseUrl: 'audioResponseUrl',
  selectedOptions: 'selectedOptions',
  orderedItems: 'orderedItems',
  highlightedWords: 'highlightedWords',
  score: 'score',
  isCorrect: 'isCorrect',
  aiFeedback: 'aiFeedback',
  detailedAnalysis: 'detailedAnalysis',
  suggestions: 'suggestions',
  timeTakenSeconds: 'timeTakenSeconds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIReportScalarFieldEnum = {
  id: 'id',
  testAttemptId: 'testAttemptId',
  overallSummary: 'overallSummary',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  suggestions: 'suggestions',
  grammarScore: 'grammarScore',
  oralFluencyScore: 'oralFluencyScore',
  pronunciationScore: 'pronunciationScore',
  vocabularyScore: 'vocabularyScore',
  discourseScore: 'discourseScore',
  spellingScore: 'spellingScore',
  sectionWiseFeedback: 'sectionWiseFeedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  paymentStatus: 'paymentStatus',
  gateway: 'gateway',
  transactionId: 'transactionId',
  orderId: 'orderId',
  purchasedItem: 'purchasedItem',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  message: 'message',
  type: 'type',
  read: 'read',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.SubscriptionPlan = exports.$Enums.SubscriptionPlan = {
  FREE: 'FREE',
  BASIC: 'BASIC',
  PREMIUM: 'PREMIUM'
};

exports.PteQuestionTypeName = exports.$Enums.PteQuestionTypeName = {
  READ_ALOUD: 'READ_ALOUD',
  REPEAT_SENTENCE: 'REPEAT_SENTENCE',
  DESCRIBE_IMAGE: 'DESCRIBE_IMAGE',
  RE_TELL_LECTURE: 'RE_TELL_LECTURE',
  ANSWER_SHORT_QUESTION: 'ANSWER_SHORT_QUESTION',
  SUMMARIZE_WRITTEN_TEXT: 'SUMMARIZE_WRITTEN_TEXT',
  WRITE_ESSAY: 'WRITE_ESSAY',
  READING_WRITING_FILL_IN_THE_BLANKS: 'READING_WRITING_FILL_IN_THE_BLANKS',
  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING: 'MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING',
  RE_ORDER_PARAGRAPHS: 'RE_ORDER_PARAGRAPHS',
  READING_FILL_IN_THE_BLANKS: 'READING_FILL_IN_THE_BLANKS',
  MULTIPLE_CHOICE_SINGLE_ANSWER_READING: 'MULTIPLE_CHOICE_SINGLE_ANSWER_READING',
  SUMMARIZE_SPOKEN_TEXT: 'SUMMARIZE_SPOKEN_TEXT',
  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING: 'MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING',
  LISTENING_FILL_IN_THE_BLANKS: 'LISTENING_FILL_IN_THE_BLANKS',
  HIGHLIGHT_CORRECT_SUMMARY: 'HIGHLIGHT_CORRECT_SUMMARY',
  MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING: 'MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING',
  SELECT_MISSING_WORD: 'SELECT_MISSING_WORD',
  HIGHLIGHT_INCORRECT_WORDS: 'HIGHLIGHT_INCORRECT_WORDS',
  WRITE_FROM_DICTATION: 'WRITE_FROM_DICTATION'
};

exports.AuthProviders = exports.$Enums.AuthProviders = {
  EMAIL_OTP: 'EMAIL_OTP',
  GOOGLE: 'GOOGLE',
  APPLE: 'APPLE'
};

exports.OtpType = exports.$Enums.OtpType = {
  LOGIN: 'LOGIN',
  REGISTRATION: 'REGISTRATION',
  PASSWORD_RESET: 'PASSWORD_RESET'
};

exports.DifficultyLevel = exports.$Enums.DifficultyLevel = {
  EASY: 'EASY',
  MEDIUM: 'MEDIUM',
  HARD: 'HARD'
};

exports.Prisma.ModelName = {
  User: 'User',
  PracticeSession: 'PracticeSession',
  PracticeResponse: 'PracticeResponse',
  OtpCode: 'OtpCode',
  Subscription: 'Subscription',
  Course: 'Course',
  CourseSection: 'CourseSection',
  Lesson: 'Lesson',
  UserCourse: 'UserCourse',
  Category: 'Category',
  CourseReview: 'CourseReview',
  UserLessonProgress: 'UserLessonProgress',
  UserSectionProgress: 'UserSectionProgress',
  LessonResource: 'LessonResource',
  PteSection: 'PteSection',
  QuestionType: 'QuestionType',
  Question: 'Question',
  Test: 'Test',
  TestAttempt: 'TestAttempt',
  UserResponse: 'UserResponse',
  QuestionResponse: 'QuestionResponse',
  AIReport: 'AIReport',
  Transaction: 'Transaction',
  Notification: 'Notification'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\My_Coding\\PTE By DEE\\PTEByDee\\Backend\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\My_Coding\\PTE By DEE\\PTEByDee\\Backend\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "6.16.2",
  "engineVersion": "1c57fdcd7e44b29b9313256c76699e91c3ac3c43",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "mongodb",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "generator client {\n  provider = \"prisma-client-js\"\n}\n\ndatasource db {\n  provider = \"mongodb\"\n  url      = env(\"DATABASE_URL\")\n}\n\n// --- CORE USER & SUBSCRIPTION MODELS ---\nmodel User {\n  id                String             @id @default(auto()) @map(\"_id\") @db.ObjectId\n  name              String\n  email             String             @unique\n  isVerified        Boolean            @default(false)\n  createdAt         DateTime           @default(now())\n  updatedAt         DateTime           @default(now()) @updatedAt\n  role              UserRole           @default(USER)\n  otpCodes          OtpCode[]\n  subscription      Subscription?\n  testAttempts      TestAttempt[]\n  courses           UserCourse[]\n  transactions      Transaction[]\n  notifications     Notification[]\n  deletedAt         DateTime? // For soft deletes\n  practiceSessions  PracticeSession[]\n  practiceResponses PracticeResponse[]\n  questionResponses QuestionResponse[]\n\n  // --- Google OAuth Fields ---\n  googleId           String? @unique\n  googleAccessToken  String?\n  googleRefreshToken String?\n\n  // --- Apple OAuth Fields ---\n  appleId           String? @unique // Unique ID from Apple (subject 'sub' claim)\n  appleRefreshToken String? // Needed to revoke tokens or potentially get new access tokens\n  appleEmail        String? // The email Apple provides (could be relay email)\n  // You might also want to store the family name and given name if provided\n  appleGivenName    String?\n  appleFamilyName   String?\n\n  profilePictureUrl   String? // Consolidate profile picture URL for any provider\n  provider            AuthProviders         @default(EMAIL_OTP) // Which provider the user last used to login\n  CourseReview        CourseReview[]\n  UserLessonProgress  UserLessonProgress[]\n  UserSectionProgress UserSectionProgress[]\n  userResponses       UserResponse[]\n}\n\n// Practice session model for tracking daily practice\nmodel PracticeSession {\n  id                String             @id @default(auto()) @map(\"_id\") @db.ObjectId\n  userId            String             @db.ObjectId\n  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)\n  sessionDate       DateTime // Date of practice session (normalized to start of day)\n  totalQuestions    Int                @default(0)\n  correctAnswers    Int                @default(0)\n  totalTimeSpent    Int                @default(0) // in seconds\n  createdAt         DateTime           @default(now())\n  updatedAt         DateTime           @updatedAt\n  practiceResponses PracticeResponse[]\n\n  @@unique([userId, sessionDate])\n}\n\n// Individual practice response model\nmodel PracticeResponse {\n  id                String              @id @default(auto()) @map(\"_id\") @db.ObjectId\n  userId            String              @db.ObjectId\n  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)\n  questionId        String              @db.ObjectId\n  question          Question            @relation(fields: [questionId], references: [id], onDelete: Cascade)\n  practiceSessionId String              @db.ObjectId\n  practiceSession   PracticeSession     @relation(fields: [practiceSessionId], references: [id], onDelete: Cascade)\n  questionType      PteQuestionTypeName\n  userResponse      Json // Store the user's response (text, selected options, etc.)\n  timeTakenSeconds  Int                 @default(0)\n  isCorrect         Boolean             @default(false)\n  score             Float               @default(0) // Score for this question (0-1 or 0-100 depending on scoring system)\n  createdAt         DateTime            @default(now())\n\n  @@index([userId, questionType])\n  @@index([userId, createdAt])\n  @@index([practiceSessionId])\n}\n\n// New OTP Code model\nmodel OtpCode {\n  id        String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  email     String\n  code      String\n  type      OtpType\n  expiresAt DateTime\n  used      Boolean  @default(false)\n  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId    String?  @db.ObjectId\n  createdAt DateTime @default(now())\n\n  @@index([email, type])\n  @@index([expiresAt])\n}\n\nmodel Subscription {\n  id                   String           @id @default(auto()) @map(\"_id\") @db.ObjectId\n  planName             SubscriptionPlan @default(FREE)\n  price                Float\n  durationInDays       Int // e.g., 30 for 30 days\n  features             String[]\n  user                 User             @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)\n  userId               String           @unique @db.ObjectId\n  startDate            DateTime         @default(now())\n  endDate              DateTime // Calculated based on startDate + durationInDays\n  createdAt            DateTime         @default(now())\n  updatedAt            DateTime         @updatedAt\n  stripeSubscriptionId String?          @unique\n  stripeCustomerId     String?\n\n  // --- FIELDS FOR FREE TRIAL ---\n  isTrial                 Boolean   @default(false) // True if this subscription record represents a free trial\n  trialStartedAt          DateTime? // When the trial began\n  trialEndsAt             DateTime? // When the trial will automatically expire/convert\n  trialConvertedToPaid    Boolean?  @default(false) // True if the trial successfully converted to a paid plan\n  trialCancellationReason String? // Optional: If user cancels trial, why?\n  // Add a field to link to the actual paid subscription if it converts\n  // convertedToSubscriptionId String? @db.ObjectId // If you create a new Subscription record upon conversion\n  // convertedToSubscription   Subscription? @relation(\"TrialConversion\", fields: [convertedToSubscriptionId], references: [id])\n  // Instead of a separate record, you might just update the existing one (simpler)\n}\n\n// --- LEARNING CONTENT MODELS ---\nmodel Course {\n  id                    String          @id @default(auto()) @map(\"_id\") @db.ObjectId\n  title                 String\n  description           String\n  coursePreviewVideoUrl String?\n  sections              CourseSection[]\n  userCourses           UserCourse[]\n  isFree                Boolean         @default(false)\n  createdAt             DateTime        @default(now())\n  updatedAt             DateTime        @updatedAt // Added updatedAt\n  imageUrl              String? // Optional: Course image URL\n  reviews               CourseReview[]\n  averageRating         Float?          @default(0.0) // Can be updated by a trigger/cron job\n  reviewCount           Int?            @default(0) // Can be updated by a trigger/cron job\n  price                 Float? // Price of the course if sold individually\n  currency              String?         @default(\"INR\") // e.g., \"USD\", \"INR\"\n  stripeProductId       String? // Stripe product ID for payments\n  stripePriceId         String? // Stripe price ID for payments\n\n  categoryIds String[] @db.ObjectId // NEW: Array of category ObjectId references\n  // instructor    User? @relation(fields: [instructorId], references: [id]) // Uncomment if you add an instructor role\n  // instructorId  String? @db.ObjectId\n}\n\nmodel CourseSection {\n  id                  String                @id @default(auto()) @map(\"_id\") @db.ObjectId\n  title               String\n  description         String?\n  videoUrl            String? // Legacy field - for backward compatibility\n  videoKey            String? // S3 key for secure video storage\n  course              Course                @relation(fields: [courseId], references: [id], onDelete: Cascade)\n  courseId            String                @db.ObjectId\n  order               Int // To define the order of sections within a course\n  lessons             Lesson[]\n  createdAt           DateTime              @default(now()) // Added createdAt for content\n  updatedAt           DateTime              @updatedAt // Added updatedAt for content\n  UserSectionProgress UserSectionProgress[]\n}\n\nmodel Lesson {\n  id                 String               @id @default(auto()) @map(\"_id\") @db.ObjectId\n  title              String\n  description        String?\n  videoUrl           String? // Legacy field - for backward compatibility\n  videoKey           String? // S3 key for secure video storage\n  textContent        String? // Any accompanying text/notes\n  audioUrl           String? // For audio-only lessons\n  section            CourseSection        @relation(fields: [sectionId], references: [id], onDelete: Cascade)\n  sectionId          String               @db.ObjectId\n  order              Int // Order of lessons within a section\n  createdAt          DateTime             @default(now()) // Added createdAt for content\n  updatedAt          DateTime             @updatedAt // Added updatedAt for content\n  // questions     Question[] // If a lesson can contain practice questions directly\n  UserLessonProgress UserLessonProgress[]\n  LessonResource     LessonResource[]\n}\n\nmodel UserCourse {\n  id          String    @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId      String    @db.ObjectId\n  course      Course    @relation(fields: [courseId], references: [id])\n  courseId    String    @db.ObjectId\n  progress    Float     @default(0.0) // Percentage of course completed\n  completed   Boolean   @default(false)\n  enrolledAt  DateTime  @default(now())\n  completedAt DateTime? // When the user completed the course\n\n  @@unique([userId, courseId]) // A user can only enroll in a course once\n  @@index([userId])\n  @@index([courseId])\n}\n\nmodel Category {\n  id          String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  name        String   @unique\n  slug        String   @unique // For friendly URLs (e.g., /courses/grammar)\n  description String?\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n  // No direct relation back to Course here\n}\n\nmodel CourseReview {\n  id        String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  rating    Int // 1 to 5 stars\n  comment   String?\n  user      User     @relation(fields: [userId], references: [id])\n  userId    String   @db.ObjectId\n  course    Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)\n  courseId  String   @db.ObjectId\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@unique([userId, courseId]) // A user can review a course only once\n  @@index([courseId, rating]) // For querying average ratings\n}\n\nmodel UserLessonProgress {\n  id              String    @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId          String    @db.ObjectId\n  lesson          Lesson    @relation(fields: [lessonId], references: [id], onDelete: Cascade)\n  lessonId        String    @db.ObjectId\n  isCompleted     Boolean   @default(false)\n  watchedDuration Float? // For video lessons, how many seconds watched\n  completedAt     DateTime? // When the lesson was completed\n  lastAccessedAt  DateTime  @default(now()) // When the lesson was last accessed\n  createdAt       DateTime  @default(now())\n  updatedAt       DateTime  @updatedAt\n\n  @@unique([userId, lessonId]) // User can only have one progress record per lesson\n  @@index([userId, isCompleted])\n  @@index([userId, lastAccessedAt])\n}\n\nmodel UserSectionProgress {\n  id             String        @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId         String        @db.ObjectId\n  section        CourseSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)\n  sectionId      String        @db.ObjectId\n  isCompleted    Boolean       @default(false)\n  completedAt    DateTime? // When the section was completed\n  lastAccessedAt DateTime      @default(now()) // When the section was last accessed\n  createdAt      DateTime      @default(now())\n  updatedAt      DateTime      @updatedAt\n\n  @@unique([userId, sectionId]) // User can only have one progress record per section\n  @@index([userId, isCompleted])\n  @@index([userId, lastAccessedAt])\n}\n\nmodel LessonResource {\n  id        String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  name      String\n  fileUrl   String // URL to the hosted file (PDF, DOCX, etc.)\n  fileType  String? // e.g., \"pdf\", \"docx\", \"zip\"\n  lesson    Lesson   @relation(fields: [lessonId], references: [id], onDelete: Cascade)\n  lessonId  String   @db.ObjectId\n  createdAt DateTime @default(now())\n}\n\n// --- PTE SIMULATION MODELS ---\n\n// Defines the major sections of a PTE Test (e.g., Speaking & Writing, Reading, Listening)\nmodel PteSection {\n  id              String         @id @default(auto()) @map(\"_id\") @db.ObjectId\n  name            String         @unique // e.g., \"Speaking & Writing\", \"Reading\", \"Listening\"\n  description     String?\n  durationMinutes Int // Expected duration for this section in a real test\n  questionTypes   QuestionType[] // All question types that belong to this section\n  order           Int?\n}\n\n// Defines a specific type of question (e.g., Read Aloud, Essay, Multiple Choice Single Answer)\nmodel QuestionType {\n  id                      String              @id @default(auto()) @map(\"_id\") @db.ObjectId\n  name                    PteQuestionTypeName @unique // Using the specific enum for question types\n  description             String? // How this question type works, general instructions\n  pteSection              PteSection          @relation(fields: [pteSectionId], references: [id], onDelete: Cascade)\n  pteSectionId            String              @db.ObjectId\n  expectedTimePerQuestion Int? // Optional: average time for this question type (for guidance)\n  questions               Question[] // All individual questions of this type\n}\n\n// Represents a specific instance of a question that can appear in a Test\nmodel Question {\n  id              String          @id @default(auto()) @map(\"_id\") @db.ObjectId\n  questionCode    String          @unique // For easy identification/referencing (e.g., \"RA_001_Test1\")\n  questionType    QuestionType    @relation(fields: [questionTypeId], references: [id])\n  questionTypeId  String          @db.ObjectId\n  difficultyLevel DifficultyLevel @default(MEDIUM) // New field for difficulty\n  // test           Test         @relation(fields: [testId], references: [id]) // Which test this question belongs to\n  // testId         String       @db.ObjectId\n  // orderInTest    Int // Order of question within a specific test\n\n  // Question content fields - tailored for PTE Academic question types\n  textContent       String? // For Read Aloud, Summarize Written Text, Essay, Reading Fill in the Blanks, R&W Fill in the Blanks, Re-order Paragraphs, Highlight Incorrect Words\n  questionStatement String? // The actual question being asked (e.g., \"What can we infer from the passage?\", \"Which of the following statements are incorrect?\")\n  audioUrl          String? // For Repeat Sentence, Re-tell Lecture, Answer Short Question, Summarize Spoken Text, Listening Fill in the Blanks, Highlight Correct Summary, Select Missing Word, Write from Dictation\n  imageUrl          String? // For Describe Image (URL to the image)\n\n  // Answer options/structure for various question types\n  options        Json? // For Multiple Choice (Single/Multiple Answer), Highlight Correct Summary, Select Missing Word\n  // Example for MCQs: [{ text: \"Option A\", isCorrect: true }, { text: \"Option B\", isCorrect: false }]\n  correctAnswers Json? // For Fill in the Blanks (array of strings), Write from Dictation (string), Repeat Sentence (string), Re-order Paragraphs (array of strings/IDs in correct order), Answer Short Question (array of strings)\n  // Example for Reading Fill in the Blanks (dropdowns): [{ gapId: \"gap1\", options: [\"A\", \"B\"], correctAnswer: \"A\" }]\n\n  // Specific constraints/metadata for question types\n  wordCountMin           Int? // For Essay, Summarize Written Text, Summarize Spoken Text (min word limit)\n  wordCountMax           Int? // For Essay, Summarize Written Text, Summarize Spoken Text (max word limit)\n  durationMillis         Int? // For audio/video duration, or time limit for recording response (e.g., for Read Aloud preparation time, recording time)\n  // For Highlight Incorrect Words\n  originalTextWithErrors String? // The text shown to the user with errors to highlight\n  incorrectWords         Json? // e.g., [\"word1\", \"word2\"] - actual incorrect words in the original text to mark\n\n  createdAt        DateTime           @default(now())\n  updatedAt        DateTime           @updatedAt\n  UserResponse     UserResponse[] // Inverse relation: a question can have many user responses across different attempts\n  PracticeResponse PracticeResponse[] // Relation to practice responses\n  QuestionResponse QuestionResponse[] // Relation to standalone question responses\n  // Keeping this for now as it's not \"non-logical\", just potentially large if eager loaded.\n  // @@index([testId])\n\n  @@index([questionTypeId])\n  @@index([difficultyLevel])\n}\n\n// Represents a full PTE Mock Test\nmodel Test {\n  id            String  @id @default(auto()) @map(\"_id\") @db.ObjectId\n  title         String // e.g., \"PTE Mock Test 1 (Academic)\"\n  description   String?\n  testType      String  @default(\"ACADEMIC\") // Could be \"ACADEMIC\", \"CORE\" - helpful for filtering\n  totalDuration Int // Total duration in minutes (e.g., 120 minutes)\n  isFree        Boolean @default(false) // Whether this test is free or requires subscription/payment\n\n  // Link questions to the test using a list of their IDs\n  questionIds String[] @db.ObjectId\n  // questions     Question[] // Questions belonging to this test\n\n  testAttempts TestAttempt[]\n  createdAt    DateTime      @default(now())\n  updatedAt    DateTime      @updatedAt\n\n  @@index([isFree])\n  @@index([testType])\n}\n\n// Represents a user's attempt at a specific test\nmodel TestAttempt {\n  id                 String    @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user               User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId             String    @db.ObjectId\n  test               Test      @relation(fields: [testId], references: [id])\n  testId             String    @db.ObjectId\n  startedAt          DateTime  @default(now())\n  completedAt        DateTime?\n  overallScore       Int? // Final overall score (10-90)\n  // Communicative skills scores (Keeping these here for easy access/display)\n  speakingScore      Int? // 10-90\n  writingScore       Int? // 10-90\n  readingScore       Int? // 10-90\n  listeningScore     Int? // 10-90\n  // Enabling skills scores (PTE specific)\n  grammarScore       Int? // 10-90\n  oralFluencyScore   Int? // 10-90\n  pronunciationScore Int? // 10-90\n  vocabularyScore    Int? // 10-90\n  discourseScore     Int? // 10-90 (for coherence/cohesion)\n  spellingScore      Int? // 10-90\n  // User's answers/responses for each question (removed for standalone question practice)\n  feedback           AIReport? // One-to-one with AIReport\n  rawPteScoreJson    Json? // To store the full detailed PTE score breakdown from AI if available\n  status             String    @default(\"IN_PROGRESS\") // IN_PROGRESS, COMPLETED, REVIEW_PENDING (if human review needed)\n  timeTakenSeconds   Int? // Timer for the overall test/sections\n\n  @@index([userId, testId])\n  @@index([userId, startedAt])\n  @@index([status])\n}\n\n// Stores the user's specific answer for individual questions (standalone practice)\nmodel UserResponse {\n  id         String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId     String   @db.ObjectId\n  question   Question @relation(fields: [questionId], references: [id])\n  questionId String   @db.ObjectId\n\n  // User's response data - highly dependent on question type\n  textResponse     String? // For Essay, Summarize Written Text, Fill in the Blanks (typed answer)\n  audioResponseUrl String? // For Read Aloud, Repeat Sentence, Describe Image, Re-tell Lecture, Answer Short Question (URL to user's recorded audio)\n  selectedOptions  String[] // For Multiple Choice (Single/Multiple Answer), Highlight Correct Summary, Select Missing Word (array of selected option IDs or values)\n  orderedItems     String[] // For Re-order Paragraphs (array of paragraph IDs/segments in user's chosen order)\n  highlightedWords String[] // For Highlight Incorrect Words (array of words user highlighted)\n\n  // Scoring specific to this question (can be derived from AIReport, or used for partial scoring)\n  questionScore    Int? // Score for this specific question (e.g., out of 1 or 2, depending on question type)\n  isCorrect        Boolean? // For questions with clear correct/incorrect answers\n  aiFeedback       String? // Direct AI feedback for this specific question (e.g., \"Pronunciation clear but content slightly off\")\n  detailedAnalysis Json? // Detailed evaluation data including word analysis, scores, etc.\n\n  timeTakenSeconds Int? // How long the user spent on this question\n  createdAt        DateTime @default(now())\n  updatedAt        DateTime @updatedAt\n\n  @@index([userId])\n  @@index([questionId])\n  @@index([userId, questionId])\n}\n\n// New model for standalone question responses (not part of a test)\nmodel QuestionResponse {\n  id         String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  userId     String   @db.ObjectId\n  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  questionId String   @db.ObjectId\n  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)\n\n  // User's response data\n  textResponse     String?\n  audioResponseUrl String?\n  selectedOptions  String[]\n  orderedItems     String[]\n  highlightedWords String[]\n\n  // AI Evaluation results\n  score            Float    @default(0) // 0-100 score from AI evaluation\n  isCorrect        Boolean  @default(false)\n  aiFeedback       String? // AI-generated feedback\n  detailedAnalysis Json? // Detailed analysis from AI\n  suggestions      String[] // AI suggestions for improvement\n\n  timeTakenSeconds Int      @default(0)\n  createdAt        DateTime @default(now())\n  updatedAt        DateTime @updatedAt\n\n  @@index([userId, questionId])\n  @@index([userId, createdAt])\n  @@index([questionId])\n}\n\n// AI-generated report for a TestAttempt\nmodel AIReport {\n  id                  String      @id @default(auto()) @map(\"_id\") @db.ObjectId\n  testAttempt         TestAttempt @relation(fields: [testAttemptId], references: [id], onDelete: Cascade)\n  testAttemptId       String      @unique @db.ObjectId // One-to-one\n  overallSummary      String // Overall summary of performance\n  strengths           String[] // Areas where the user performed well\n  weaknesses          String[] // Areas needing improvement\n  suggestions         String[] // Actionable advice for improvement\n  // Detailed breakdown of scores for specific skills (Keeping these here as a comprehensive AI report)\n  grammarScore        Int?\n  oralFluencyScore    Int?\n  pronunciationScore  Int?\n  vocabularyScore     Int?\n  discourseScore      Int?\n  spellingScore       Int?\n  // Detailed feedback for specific question types or sections\n  sectionWiseFeedback Json? // e.g., { \"Speaking & Writing\": { fluency: \"Good\", pronunciation: \"Needs improvement in 'th' sounds\" } }\n  createdAt           DateTime    @default(now())\n  updatedAt           DateTime    @updatedAt\n}\n\n// --- TRANSACTION & NOTIFICATION MODELS ---\nmodel Transaction {\n  id            String        @id @default(auto()) @map(\"_id\") @db.ObjectId\n  user          User          @relation(fields: [userId], references: [id])\n  userId        String        @db.ObjectId\n  amount        Float\n  paymentStatus PaymentStatus @default(PENDING)\n  gateway       String // e.g., \"Razorpay\", \"Stripe\"\n  transactionId String? // ID from the payment gateway\n  orderId       String? // Your internal order ID if different from transactionId\n  purchasedItem String? // What was purchased (e.g., \"Premium Plan\", \"PTE Mock Test 3\")\n  createdAt     DateTime      @default(now())\n\n  @@unique([transactionId, userId])\n  @@index([gateway])\n  @@index([paymentStatus])\n  @@index([userId, createdAt])\n}\n\nmodel Notification {\n  id        String   @id @default(auto()) @map(\"_id\") @db.ObjectId\n  userId    String   @db.ObjectId\n  user      User     @relation(fields: [userId], references: [id])\n  message   String\n  type      String // e.g., \"SYSTEM\", \"COURSE_UPDATE\", \"TEST_FEEDBACK\"\n  read      Boolean  @default(false)\n  createdAt DateTime @default(now())\n\n  @@index([userId, read])\n  @@index([createdAt])\n}\n\n// Enums for better type safety and clarity\nenum UserRole {\n  USER\n  ADMIN\n}\n\nenum PaymentStatus {\n  PENDING\n  SUCCESS\n  FAILED\n  REFUNDED\n}\n\nenum SubscriptionPlan {\n  FREE\n  BASIC\n  PREMIUM\n}\n\n// Enum for PTE Academic Question Types (Crucial for simulation)\nenum PteQuestionTypeName {\n  // Speaking (AI Scored)\n  READ_ALOUD\n  REPEAT_SENTENCE\n  DESCRIBE_IMAGE\n  RE_TELL_LECTURE\n  ANSWER_SHORT_QUESTION\n\n  // Writing (AI Scored)\n  SUMMARIZE_WRITTEN_TEXT\n  WRITE_ESSAY\n\n  // Reading\n  READING_WRITING_FILL_IN_THE_BLANKS // Combined skill type\n  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING // Specific to Reading\n  RE_ORDER_PARAGRAPHS\n  READING_FILL_IN_THE_BLANKS // Specific to Reading\n  MULTIPLE_CHOICE_SINGLE_ANSWER_READING // Specific to Reading\n\n  // Listening\n  SUMMARIZE_SPOKEN_TEXT // AI Scored\n  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING // Specific to Listening\n  LISTENING_FILL_IN_THE_BLANKS // Specific to Listening\n  HIGHLIGHT_CORRECT_SUMMARY\n  MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING // Specific to Listening\n  SELECT_MISSING_WORD\n  HIGHLIGHT_INCORRECT_WORDS\n  WRITE_FROM_DICTATION\n}\n\nenum AuthProviders {\n  EMAIL_OTP\n  GOOGLE\n  APPLE\n}\n\nenum OtpType {\n  LOGIN\n  REGISTRATION\n  PASSWORD_RESET\n}\n\n// Enum for question difficulty levels\nenum DifficultyLevel {\n  EASY\n  MEDIUM\n  HARD\n}\n",
  "inlineSchemaHash": "bb863eaed47affad73759b7beb563dd972d028d6f786fb6cedda14a0d2e26c8b",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isVerified\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"role\",\"kind\":\"enum\",\"type\":\"UserRole\"},{\"name\":\"otpCodes\",\"kind\":\"object\",\"type\":\"OtpCode\",\"relationName\":\"OtpCodeToUser\"},{\"name\":\"subscription\",\"kind\":\"object\",\"type\":\"Subscription\",\"relationName\":\"SubscriptionToUser\"},{\"name\":\"testAttempts\",\"kind\":\"object\",\"type\":\"TestAttempt\",\"relationName\":\"TestAttemptToUser\"},{\"name\":\"courses\",\"kind\":\"object\",\"type\":\"UserCourse\",\"relationName\":\"UserToUserCourse\"},{\"name\":\"transactions\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"TransactionToUser\"},{\"name\":\"notifications\",\"kind\":\"object\",\"type\":\"Notification\",\"relationName\":\"NotificationToUser\"},{\"name\":\"deletedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"practiceSessions\",\"kind\":\"object\",\"type\":\"PracticeSession\",\"relationName\":\"PracticeSessionToUser\"},{\"name\":\"practiceResponses\",\"kind\":\"object\",\"type\":\"PracticeResponse\",\"relationName\":\"PracticeResponseToUser\"},{\"name\":\"questionResponses\",\"kind\":\"object\",\"type\":\"QuestionResponse\",\"relationName\":\"QuestionResponseToUser\"},{\"name\":\"googleId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"googleAccessToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"googleRefreshToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"appleId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"appleRefreshToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"appleEmail\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"appleGivenName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"appleFamilyName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"profilePictureUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"provider\",\"kind\":\"enum\",\"type\":\"AuthProviders\"},{\"name\":\"CourseReview\",\"kind\":\"object\",\"type\":\"CourseReview\",\"relationName\":\"CourseReviewToUser\"},{\"name\":\"UserLessonProgress\",\"kind\":\"object\",\"type\":\"UserLessonProgress\",\"relationName\":\"UserToUserLessonProgress\"},{\"name\":\"UserSectionProgress\",\"kind\":\"object\",\"type\":\"UserSectionProgress\",\"relationName\":\"UserToUserSectionProgress\"},{\"name\":\"userResponses\",\"kind\":\"object\",\"type\":\"UserResponse\",\"relationName\":\"UserToUserResponse\"}],\"dbName\":null},\"PracticeSession\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"PracticeSessionToUser\"},{\"name\":\"sessionDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"totalQuestions\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"correctAnswers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalTimeSpent\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"practiceResponses\",\"kind\":\"object\",\"type\":\"PracticeResponse\",\"relationName\":\"PracticeResponseToPracticeSession\"}],\"dbName\":null},\"PracticeResponse\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"PracticeResponseToUser\"},{\"name\":\"questionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"question\",\"kind\":\"object\",\"type\":\"Question\",\"relationName\":\"PracticeResponseToQuestion\"},{\"name\":\"practiceSessionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"practiceSession\",\"kind\":\"object\",\"type\":\"PracticeSession\",\"relationName\":\"PracticeResponseToPracticeSession\"},{\"name\":\"questionType\",\"kind\":\"enum\",\"type\":\"PteQuestionTypeName\"},{\"name\":\"userResponse\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"timeTakenSeconds\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isCorrect\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"score\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"OtpCode\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"code\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"OtpType\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"used\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"OtpCodeToUser\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Subscription\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"planName\",\"kind\":\"enum\",\"type\":\"SubscriptionPlan\"},{\"name\":\"price\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"durationInDays\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"features\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SubscriptionToUser\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"startDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"endDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"stripeSubscriptionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripeCustomerId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isTrial\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"trialStartedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"trialEndsAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"trialConvertedToPaid\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"trialCancellationReason\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"Course\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"coursePreviewVideoUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"sections\",\"kind\":\"object\",\"type\":\"CourseSection\",\"relationName\":\"CourseToCourseSection\"},{\"name\":\"userCourses\",\"kind\":\"object\",\"type\":\"UserCourse\",\"relationName\":\"CourseToUserCourse\"},{\"name\":\"isFree\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"reviews\",\"kind\":\"object\",\"type\":\"CourseReview\",\"relationName\":\"CourseToCourseReview\"},{\"name\":\"averageRating\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"reviewCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"price\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"currency\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripeProductId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripePriceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryIds\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"CourseSection\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"videoUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"videoKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"course\",\"kind\":\"object\",\"type\":\"Course\",\"relationName\":\"CourseToCourseSection\"},{\"name\":\"courseId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"order\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lessons\",\"kind\":\"object\",\"type\":\"Lesson\",\"relationName\":\"CourseSectionToLesson\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"UserSectionProgress\",\"kind\":\"object\",\"type\":\"UserSectionProgress\",\"relationName\":\"CourseSectionToUserSectionProgress\"}],\"dbName\":null},\"Lesson\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"videoUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"videoKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"textContent\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"audioUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"section\",\"kind\":\"object\",\"type\":\"CourseSection\",\"relationName\":\"CourseSectionToLesson\"},{\"name\":\"sectionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"order\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"UserLessonProgress\",\"kind\":\"object\",\"type\":\"UserLessonProgress\",\"relationName\":\"LessonToUserLessonProgress\"},{\"name\":\"LessonResource\",\"kind\":\"object\",\"type\":\"LessonResource\",\"relationName\":\"LessonToLessonResource\"}],\"dbName\":null},\"UserCourse\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserCourse\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"course\",\"kind\":\"object\",\"type\":\"Course\",\"relationName\":\"CourseToUserCourse\"},{\"name\":\"courseId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"progress\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"completed\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"enrolledAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Category\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"slug\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"CourseReview\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"rating\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"comment\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"CourseReviewToUser\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"course\",\"kind\":\"object\",\"type\":\"Course\",\"relationName\":\"CourseToCourseReview\"},{\"name\":\"courseId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"UserLessonProgress\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserLessonProgress\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"lesson\",\"kind\":\"object\",\"type\":\"Lesson\",\"relationName\":\"LessonToUserLessonProgress\"},{\"name\":\"lessonId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isCompleted\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"watchedDuration\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastAccessedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"UserSectionProgress\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserSectionProgress\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"section\",\"kind\":\"object\",\"type\":\"CourseSection\",\"relationName\":\"CourseSectionToUserSectionProgress\"},{\"name\":\"sectionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isCompleted\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastAccessedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"LessonResource\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"fileUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"fileType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"lesson\",\"kind\":\"object\",\"type\":\"Lesson\",\"relationName\":\"LessonToLessonResource\"},{\"name\":\"lessonId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"PteSection\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"durationMinutes\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"questionTypes\",\"kind\":\"object\",\"type\":\"QuestionType\",\"relationName\":\"PteSectionToQuestionType\"},{\"name\":\"order\",\"kind\":\"scalar\",\"type\":\"Int\"}],\"dbName\":null},\"QuestionType\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"name\",\"kind\":\"enum\",\"type\":\"PteQuestionTypeName\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"pteSection\",\"kind\":\"object\",\"type\":\"PteSection\",\"relationName\":\"PteSectionToQuestionType\"},{\"name\":\"pteSectionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expectedTimePerQuestion\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"questions\",\"kind\":\"object\",\"type\":\"Question\",\"relationName\":\"QuestionToQuestionType\"}],\"dbName\":null},\"Question\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"questionCode\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"questionType\",\"kind\":\"object\",\"type\":\"QuestionType\",\"relationName\":\"QuestionToQuestionType\"},{\"name\":\"questionTypeId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"difficultyLevel\",\"kind\":\"enum\",\"type\":\"DifficultyLevel\"},{\"name\":\"textContent\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"questionStatement\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"audioUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"options\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"correctAnswers\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"wordCountMin\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"wordCountMax\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"durationMillis\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"originalTextWithErrors\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"incorrectWords\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"UserResponse\",\"kind\":\"object\",\"type\":\"UserResponse\",\"relationName\":\"QuestionToUserResponse\"},{\"name\":\"PracticeResponse\",\"kind\":\"object\",\"type\":\"PracticeResponse\",\"relationName\":\"PracticeResponseToQuestion\"},{\"name\":\"QuestionResponse\",\"kind\":\"object\",\"type\":\"QuestionResponse\",\"relationName\":\"QuestionToQuestionResponse\"}],\"dbName\":null},\"Test\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"testType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"totalDuration\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isFree\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"questionIds\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"testAttempts\",\"kind\":\"object\",\"type\":\"TestAttempt\",\"relationName\":\"TestToTestAttempt\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"TestAttempt\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TestAttemptToUser\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"test\",\"kind\":\"object\",\"type\":\"Test\",\"relationName\":\"TestToTestAttempt\"},{\"name\":\"testId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"startedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"overallScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"speakingScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"writingScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"readingScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"listeningScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"grammarScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"oralFluencyScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"pronunciationScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"vocabularyScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"discourseScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"spellingScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"feedback\",\"kind\":\"object\",\"type\":\"AIReport\",\"relationName\":\"AIReportToTestAttempt\"},{\"name\":\"rawPteScoreJson\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"status\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timeTakenSeconds\",\"kind\":\"scalar\",\"type\":\"Int\"}],\"dbName\":null},\"UserResponse\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserResponse\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"question\",\"kind\":\"object\",\"type\":\"Question\",\"relationName\":\"QuestionToUserResponse\"},{\"name\":\"questionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"textResponse\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"audioResponseUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"selectedOptions\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"orderedItems\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"highlightedWords\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"questionScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"isCorrect\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"aiFeedback\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"detailedAnalysis\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"timeTakenSeconds\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"QuestionResponse\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"QuestionResponseToUser\"},{\"name\":\"questionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"question\",\"kind\":\"object\",\"type\":\"Question\",\"relationName\":\"QuestionToQuestionResponse\"},{\"name\":\"textResponse\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"audioResponseUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"selectedOptions\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"orderedItems\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"highlightedWords\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"score\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"isCorrect\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"aiFeedback\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"detailedAnalysis\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"suggestions\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timeTakenSeconds\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"AIReport\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"testAttempt\",\"kind\":\"object\",\"type\":\"TestAttempt\",\"relationName\":\"AIReportToTestAttempt\"},{\"name\":\"testAttemptId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"overallSummary\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"strengths\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"weaknesses\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"suggestions\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"grammarScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"oralFluencyScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"pronunciationScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"vocabularyScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"discourseScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"spellingScore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"sectionWiseFeedback\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Transaction\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TransactionToUser\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"paymentStatus\",\"kind\":\"enum\",\"type\":\"PaymentStatus\"},{\"name\":\"gateway\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"transactionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"orderId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"purchasedItem\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Notification\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"_id\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"NotificationToUser\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"read\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: async () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

